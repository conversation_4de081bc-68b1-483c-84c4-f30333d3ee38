<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改房屋')" />
    <style>
        .binding-info-display {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            border: 1px solid #e9ecef;
        }

        .binding-text-item {
            margin-bottom: 10px;
            padding: 8px 0;
            display: flex;
            align-items: center;
        }

        .binding-text-item:last-child {
            margin-bottom: 0;
        }

        .binding-label {
            font-weight: 600;
            color: #333;
            min-width: 80px;
            display: inline-block;
        }

        .binding-content {
            flex: 1;
            color: #666;
            margin-right: 10px;
        }

        .binding-manage-link {
            color: #007bff;
            text-decoration: none;
            font-size: 12px;
            padding: 2px 8px;
            border: 1px solid #007bff;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .binding-manage-link:hover {
            background-color: #007bff;
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-house-edit">
            <input name="house_id" type="hidden" th:value="${house.house_id}">
            <input name="buildingId" type="hidden" th:value="${house.building_id}">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">楼栋：</label>
                <div class="col-sm-4">
                    <select name="building_id" class="form-control" data-url="/queryBuilding" onchange="queryUnit(this.value);" required>
                        <option value="">请选择楼栋</option>
                    </select>
                </div>
                <label class="col-sm-2 control-label is-required">单元：</label>
                <div class="col-sm-4">
                    <select name="unit_id" class="form-control" data-url="/queryUnit" required>
                        <option value="">请选择单元</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">房间号：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="room" required>
                </div>
                <label class="col-sm-2 control-label">房屋状态：</label>
                <div class="col-sm-4">                   
                    <select name="house_status" class="form-control">
                        <option value="">请选择房屋状态</option> 
                        <option value="2002">未销售</option> 
                        <option value="2001">已入住</option>
                        <option value="2003">已交房</option>
                        <option value="2005">已装修</option> 
                        <option value="2004">未入住</option>
                        <option value="2009">装修中</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">楼层：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="floor">
                </div>
                <label class="col-sm-2 control-label is-required">房屋类型：</label>
                <div class="col-sm-4">
                    <select name="house_type" class="form-control" th:with="type=${@dict.getType('house_type')}">
                        <option value="">请选择房屋类型</option>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:selected="${dict.dictValue == house.house_type}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">建筑面积：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="number" name="total_area" step="0.01" required>
                </div>
                <label class="col-sm-2 control-label is-required">室内面积：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="number" name="use_area" step="0.01" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">房产编号：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="property_no">
                </div>
                <label class="col-sm-2 control-label">房屋标签：</label>
                <div class="col-sm-4">
                    <select name="house_tag" class="form-control">
                        <option value="">请选择房屋标签</option>
                        <option value="叠加洋房">叠加洋房</option>
                        <option value="多层住宅">多层住宅</option>
                        <option value="联排别墅">联排别墅</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-10">
                    <textarea name="remark" class="form-control"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">绑定信息：</label>
                <div class="col-sm-10">
                    <!-- 绑定信息展示区域 -->
                    <div id="bindingInfoDisplay" class="binding-info-display">
                        <div id="ownerBindingInfo" class="binding-text-item">
                            <span class="binding-label">绑定业主：</span>
                            <span class="binding-content">暂无绑定业主</span>
                            <a href="javascript:void(0);" onclick="manageBindings();" class="binding-manage-link">去修改</a>
                        </div>
                        <div id="parkingBindingInfo" class="binding-text-item">
                            <span class="binding-label">绑定车位：</span>
                            <span class="binding-content">暂无绑定车位</span>
                            <a href="javascript:void(0);" onclick="manageBindings();" class="binding-manage-link">去修改</a>
                        </div>
                        <div id="vehicleBindingInfo" class="binding-text-item">
                            <span class="binding-label">绑定车辆：</span>
                            <span class="binding-content">暂无绑定车辆</span>
                            <a href="javascript:void(0);" onclick="manageBindings();" class="binding-manage-link">去修改</a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "oc/house";
        
        $("#form-house-edit").validate({
            focusCleanup: true
        });

        $(function() {
            $('#form-house-edit').renderSelect({prefix:prefix}, function() {
                $('#form-house-edit').renderForm({url:prefix+'/record'}, function(res) {
                    // 加载绑定信息
                    loadBindingInfo();
                });
            });
        });

        function queryUnit(buildingId) {
            $("select[name='unit_id']").renderSelect({url:prefix+'/queryUnit',data:{buildingId:buildingId}}, function() {
                
            });
        
        }

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-house-edit').serialize());
            }
        }

        function manageBindings() {
            var houseId = $("input[name='house_id']").val();
            if (!houseId) {
                $.modal.alertWarning("请先保存房屋信息");
                return;
            }
            $.modal.popupRight("房屋绑定管理", prefix + "/houseBindings/" + houseId, function() {
                // 弹窗关闭后刷新绑定信息
                loadBindingInfo();
            });
        }

        // 加载绑定信息
        function loadBindingInfo() {
            var houseId = $("input[name='house_id']").val();
            if (!houseId) {
                return;
            }

            $.ajax({
                url: prefix + "/getHouseBindingData",
                type: "POST",
                data: { houseId: houseId },
                success: function(res) {
                    if (res.code === 0) {
                        var data = res.data;
                        // 渲染业主绑定信息
                        renderOwnerBindingInfo(data.owners || []);
                        // 渲染车位绑定信息
                        renderParkingBindingInfo(data.parkings || []);
                        // 渲染车辆绑定信息
                        renderVehicleBindingInfo(data.vehicles || []);
                    }
                },
                error: function() {
                    console.error("加载绑定信息失败");
                }
            });
        }

        // 渲染业主绑定信息
        function renderOwnerBindingInfo(data) {
            var contentText = '';
            if (data && data.length > 0) {
                var displayItems = [];
                for (var i = 0; i < data.length; i++) {
                    var owner = data[i];
                    var ownerText = owner.owner_name || '业主';
                    if (owner.mobile) {
                        ownerText += '(' + owner.mobile + ')';
                    }
                    displayItems.push(ownerText);
                }
                contentText = displayItems.join('、');
            } else {
                contentText = '暂无绑定业主';
            }
            $('#ownerBindingInfo .binding-content').text(contentText);
        }

        // 渲染车位绑定信息
        function renderParkingBindingInfo(data) {
            var contentText = '';
            if (data && data.length > 0) {
                var displayItems = [];
                for (var i = 0; i < data.length; i++) {
                    var parking = data[i];
                    displayItems.push(parking.parking_no || '车位');
                }
                contentText = displayItems.join('、');
            } else {
                contentText = '暂无绑定车位';
            }
            $('#parkingBindingInfo .binding-content').text(contentText);
        }

        // 渲染车辆绑定信息
        function renderVehicleBindingInfo(data) {
            var contentText = '';
            if (data && data.length > 0) {
                var displayItems = [];
                for (var i = 0; i < data.length; i++) {
                    var vehicle = data[i];
                    var vehicleText = vehicle.plate_no || '车辆';
                    if (vehicle.vehicle_brand) {
                        vehicleText += '(' + vehicle.vehicle_brand + ')';
                    }
                    displayItems.push(vehicleText);
                }
                contentText = displayItems.join('、');
            } else {
                contentText = '暂无绑定车辆';
            }
            $('#vehicleBindingInfo .binding-content').text(contentText);
        }

    </script>
</body>
</html> 