<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('业主管理')" />
	<th:block th:include="include :: layout-latest-css" />
	<th:block th:include="include :: ztree-css" />
	<style type="text/css">
		#treeSearchInput{width: 130px;}
	</style>
</head>
<body class="gray-bg">
	<div class="ui-layout-west">
		<div class="box box-main">
			<div class="box-header">
				<div class="box-title">
					<i class="fa fa-sitemap"></i> 楼栋单元
				</div>
				<div class="box-tools pull-right">
					<button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
					<button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
					<button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新"><i class="fa fa-refresh"></i></button>
				</div>
			</div>
			<div class="ui-layout-content">
				<div style="padding: 0px;padding-bottom: 5px;">
					<div class="input-group" style="margin-bottom: 0px;">
						<input type="text" id="treeSearchInput" placeholder="搜索楼栋或单元" class="form-control form-control-sm" onkeypress="if(event.keyCode==13) searchTree()"/>
						<span class="input-group-btn">
							<button class="btn btn-sm btn-default" type="button" onclick="searchTree()"><i class="fa fa-search"></i></button>
							<button class="btn btn-sm btn-default" type="button" onclick="clearTreeSearch()"><i class="fa fa-times"></i></button>
						</span>
					</div>
				</div>
				<div id="buildingTree" class="ztree"></div>
			</div>
		</div>
	</div>

	<div class="ui-layout-center">
		<div class="container-div">
			<div class="row">
				<!-- 搜索区域 -->
				<div class="col-sm-12 search-collapse">
					<form id="config-form">
						<input type="hidden" name="buildingId" id="buildingId"/>
						<input type="hidden" name="unitId" id="unitId"/>
						<div class="select-list">
							<ul>
								<li>
									姓名：<input type="text" name="owner_name" onkeypress="if(event.keyCode==13) $.table.search()"/>
								</li>
								<li>
									手机号码：<input type="text" name="mobile" onkeypress="if(event.keyCode==13) $.table.search()"/>
								</li>
								<li>
									楼栋单元：<input type="text" name="buildingName" placeholder="请选择左侧楼栋单元" readonly/>
								</li>
								<li style="display: none;">
									<input type="text" name="unitName" readonly/>
								</li>
								<li>
									住户状态：<select name="is_live">
										<option value="">全部</option>
										<option value="1">已入住</option>
										<option value="0">未入住</option>
									</select>
								</li>
								<li>
									<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
									<a class="btn btn-warning btn-rounded btn-sm" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
								</li>
							</ul>
						</div>
					</form>
				</div>

				<!-- 工具栏和表格区域 -->
				<div class="btn-group-sm" id="toolbar" role="group">
					<a class="btn btn-success" onclick="$.operate.add()">
						<i class="fa fa-plus"></i> 新增住户
					</a>
					<a class="btn btn-danger ml-10 multiple disabled" onclick="$.operate.removeAll()">
						<i class="fa fa-remove"></i> 删除
					</a>
					<a class="btn btn-info ml-10 hidden" onclick="importData()">
						<i class="fa fa-send"></i> 批量导入
					</a>
				</div>
				<div class="col-sm-12 select-table table-striped">
					<table id="bootstrap-table"></table>
				</div>
			</div>
		</div>
	</div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: layout-latest-js" />
    <th:block th:include="include :: ztree-js" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/owner";

        $(function() {
            var panehHidden = false;
            if ($(this).width() < 769) {
                panehHidden = true;
            }
            $('body').layout({ initClosed: panehHidden, west__size: 220, resizeWithWindow: false });

            var buildingId = /*[[${buildingId}]]*/ null;
            var unitId = /*[[${unitId}]]*/ null;

            if (buildingId) {
                $("#buildingId").val(buildingId);
            }
            if (unitId) {
                $("#unitId").val(unitId);
            }

            loadBuildingZTree();

            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "业主",
				layer:{
					area:['800px','550px'],
					offset: '70px'
				},
                columns: [{
                    checkbox: true
                },
                {
                    field: 'owner_id',
                    title: '业主ID',
                    visible: false
                },
                {
                    field: 'owner_name',
                    title: '姓名',
                    formatter: function(value, row, index) {
                        return '<a href="javascript:void(0)" onclick="viewDetail(\'' + row.owner_id + '\')" style="color: #007bff;">' + value + '</a>';
                    }
                },
				{
					field: 'gender',
					title: '性别',
					visible: false,
					formatter: function(value, row, index) {
						if (value == 'M') return '男';
						else if (value == 'F') return '女';
						return '-';
					}
				},
                {
                    field: 'mobile',
                    title: '手机号'
                },{
					field: 'house_count',
                    title: '房屋数',
					visible: false,
					formatter: function(value, row, index) {
						return value + '套';
					}
                },{
                    field: 'house_info',
                    title: '房屋信息',
                    formatter: function(value, row, index) {
                        var houseInfo = value || '-';
                        return houseInfo;
                    }
                },
				{
					field: 'is_live',
					title: '入住状态',
					formatter: function(value, row, index) {
						if (value == '1') return '已入住';
						else if (value == '0') return '未入住';
						return '-';
					}
				},
				{
					field: 'role',
					title: '人员角色',
					formatter: function(value, row, index) {
						var roleDatas = [
							{ dictValue: "1", dictLabel: "业主" },
							{ dictValue: "2", dictLabel: "家庭成员" },
							{ dictValue: "3", dictLabel: "租户" }
						];
						return $.table.selectDictLabel(roleDatas, value) || '-';
					}
				},
                {
                    field: 'car_info',
                    title: '绑定车辆',
                    align: 'center',
					formatter: function(value, row, index) {
						var carInfo = '-';
						if(value){
							carInfo = value;
						}
						return carInfo;
					}
                },
				{
					field: 'parking_no',
					title: '绑定车位',
					formatter: function(value, row, index) {
						var parkingInfo = '-';
						if(value){
							parkingInfo = value
						}
						return parkingInfo;
					}
				},
                {
                    field: 'remark',
                    title: '备注',
                    align: 'center',
					formatter: function(value, row, index) {
						return value == null ? '-' : value.substring(0, 10);
					}
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
						actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.owner_id + '\')"><i class="fa fa-edit"></i> 编辑</a> ');
                        actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.owner_id + '\')"><i class="fa fa-remove"></i> 删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);

            // 如果有URL参数，立即触发搜索
            if ((buildingId && buildingId !== 'null' && buildingId !== '') ||
                (unitId && unitId !== 'null' && unitId !== '')) {
                setTimeout(function() {
                    $.table.search();
                }, 100);
            }
        });

        function viewDetail(ownerId) {
            var url = prefix + "/detail/" + ownerId;
            $.modal.popupRight("住户详情", url);
        }

        function importData() {
			$.modal.open("导入业主数据", prefix + "/importPage");
		}

		function manageHouseBindings(ownerId) {
			$.modal.popupRight({title:"管理房屋绑定",url:prefix + "/houseBindings/" + ownerId,area:['700px','100%'],end:function(){
				$.table.search();
			}});
		}

        function loadBuildingZTree() {
            $.ajax({
                url: ctx + "oc/building/tree",
                type: "GET",
                success: function(res) {
                    if (res.code == 0) {
                        var zNodes = convertToZTreeNodes(res.data);
                        var setting = {
                            data: {
                                simpleData: {
                                    enable: true,
                                    idKey: "id",
                                    pIdKey: "pId",
                                    rootPId: null
                                }
                            },
                            view: {
                                showIcon: true
                            },
                            callback: {
                                onClick: function(event, treeId, treeNode) {
                                    if(treeNode.type === 'building') {
                                        $("#buildingId").val(treeNode.id);
                                        $("#unitId").val("");
                                        $("input[name='buildingName']").val(treeNode.name);
                                        $("input[name='unitName']").val("");
                                    } else if(treeNode.type === 'unit') {
                                        $("#buildingId").val(treeNode.pId);
                                        $("#unitId").val(treeNode.id);
                                        $("input[name='buildingName']").val(treeNode.buildingName);
                                        $("input[name='unitName']").val(treeNode.name);
                                    }
                                    $.table.search();
                                }
                            }
                        };
                        $.fn.zTree.init($("#buildingTree"), setting, zNodes);
                    } else {
                        $.modal.alertError("加载楼栋单元树失败：" + res.msg);
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.alertError("加载楼栋单元树失败：" + error);
                }
            });
        }

        // 转换接口数据为ztree扁平结构
        function convertToZTreeNodes(data) {
            var nodes = [];
            $.each(data, function(i, building) {
                var bNode = {
                    id: building.buildingId,
                    pId: null,
                    name: building.buildingName.endsWith('栋') ? building.buildingName : building.buildingName + '栋',
                    type: 'building',
                    open: false
                };
                nodes.push(bNode);
                if(building.children && building.children.length > 0) {
                    $.each(building.children, function(j, unit) {
                        nodes.push({
                            id: unit.unitId,
                            pId: building.buildingId,
                            name: unit.unitName.endsWith('单元') ? unit.unitName : unit.unitName + '单元',
                            type: 'unit',
                            buildingId: building.buildingId,
                            buildingName: building.buildingName.endsWith('栋') ? building.buildingName : building.buildingName + '栋'
                        });
                    });
                }
            });
            return nodes;
        }

        // 展开所有节点
        $('#btnExpand').click(function() {
            var treeObj = $.fn.zTree.getZTreeObj("buildingTree");
            treeObj.expandAll(true);
            $(this).hide();
            $('#btnCollapse').show();
        });

        // 折叠所有节点
        $('#btnCollapse').click(function() {
            var treeObj = $.fn.zTree.getZTreeObj("buildingTree");
            treeObj.expandAll(false);
            $(this).hide();
            $('#btnExpand').show();
        });

        // 刷新树
        $('#btnRefresh').click(function() {
            loadBuildingZTree();
        });

        // 自定义重置
        function resetPre() {
            $("#config-form")[0].reset();
            $("#buildingId").val("");
            $("#unitId").val("");
            $.table.search();
        }

        // 搜索树节点
        function searchTree() {
            var keyword = $("#treeSearchInput").val();
            var treeObj = $.fn.zTree.getZTreeObj("buildingTree");
            if (!treeObj) return;

            if (keyword) {
                // 获取所有节点
                var allNodes = treeObj.getNodes();
                var nodesToShow = [];

                // 递归搜索所有节点
                function searchNodes(nodes) {
                    for (var i = 0; i < nodes.length; i++) {
                        var node = nodes[i];
                        if (node.name && node.name.indexOf(keyword) !== -1) {
                            // 匹配的节点
                            nodesToShow.push(node);

                            // 添加所有父节点
                            var parent = node.getParentNode();
                            while (parent) {
                                if (nodesToShow.indexOf(parent) === -1) {
                                    nodesToShow.push(parent);
                                }
                                parent = parent.getParentNode();
                            }

                            // 添加所有子节点
                            function addChildNodes(parentNode) {
                                if (parentNode.children && parentNode.children.length > 0) {
                                    for (var j = 0; j < parentNode.children.length; j++) {
                                        var childNode = parentNode.children[j];
                                        if (nodesToShow.indexOf(childNode) === -1) {
                                            nodesToShow.push(childNode);
                                        }
                                        addChildNodes(childNode);
                                    }
                                }
                            }
                            addChildNodes(node);
                        }
                        if (node.children && node.children.length > 0) {
                            searchNodes(node.children);
                        }
                    }
                }

                searchNodes(allNodes);

                if (nodesToShow.length > 0) {
                    // 隐藏所有节点
                    function hideAllNodes(nodes) {
                        for (var i = 0; i < nodes.length; i++) {
                            treeObj.hideNode(nodes[i]);
                            if (nodes[i].children && nodes[i].children.length > 0) {
                                hideAllNodes(nodes[i].children);
                            }
                        }
                    }
                    hideAllNodes(allNodes);

                    // 显示需要显示的节点
                    for (var i = 0; i < nodesToShow.length; i++) {
                        treeObj.showNode(nodesToShow[i]);
                    }

                    // 展开父节点并选中第一个匹配的节点
                    var firstMatchNode = null;
                    for (var i = 0; i < nodesToShow.length; i++) {
                        var node = nodesToShow[i];
                        if (node.name && node.name.indexOf(keyword) !== -1) {
                            if (!firstMatchNode) {
                                firstMatchNode = node;
                            }
                            // 展开匹配节点的父节点
                            var parent = node.getParentNode();
                            while (parent) {
                                treeObj.expandNode(parent, true, false, false);
                                parent = parent.getParentNode();
                            }
                        }
                    }

                    if (firstMatchNode) {
                        treeObj.selectNode(firstMatchNode, true);
                    }
                } else {
                    $.modal.alertWarning("未找到匹配的楼栋或单元");
                }
            } else {
                // 清空搜索，显示所有节点
                var allNodes = treeObj.getNodes();
                function showAllNodes(nodes) {
                    for (var i = 0; i < nodes.length; i++) {
                        treeObj.showNode(nodes[i]);
                        if (nodes[i].children && nodes[i].children.length > 0) {
                            showAllNodes(nodes[i].children);
                        }
                    }
                }
                showAllNodes(allNodes);
                treeObj.expandAll(false);
            }
        }

        // 清空树搜索
        function clearTreeSearch() {
            $("#treeSearchInput").val("");
            searchTree(); // 调用搜索函数来重置显示
        }
    </script>
</body>
</html>