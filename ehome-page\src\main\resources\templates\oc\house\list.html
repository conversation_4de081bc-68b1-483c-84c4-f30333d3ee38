<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('房屋信息列表')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
    <style type="text/css">
        #treeSearchInput{width: 130px;}
    </style>

</head>
<body class="gray-bg">   
    <div class="ui-layout-west">
		<div class="box box-main">
			<div class="box-header">
				<div class="box-title">
					<i class="fa fa-sitemap"></i> 房屋管理
				</div>
				<div class="box-tools pull-right">
				    <a type="button" class="btn btn-box-tool" href="javascript:void(0)" onclick="buildingMgr()" title="管理楼栋"><i class="fa fa-edit"></i></a>
					<button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
					<button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
					<button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新部门"><i class="fa fa-refresh"></i></button>
				</div>
			</div>
			<div class="ui-layout-content">
				<div style="padding: 0px;padding-bottom: 5px;">
					<div class="input-group" style="margin-bottom: 0px;">
						<input type="text" id="treeSearchInput" placeholder="搜索楼栋或单元" class="form-control form-control-sm" onkeypress="if(event.keyCode==13) searchTree()"/>
						<span class="input-group-btn">
							<button class="btn btn-sm btn-default" type="button" onclick="searchTree()"><i class="fa fa-search"></i></button>
							<button class="btn btn-sm btn-default" type="button" onclick="clearTreeSearch()"><i class="fa fa-times"></i></button>
						</span>
					</div>
				</div>
				<div id="buildingTree" class="ztree"></div>
			</div>
		</div>
	</div>

    <div class="ui-layout-center">
        <div class="container-div">
            <div class="row">
                <!-- 搜索区域 -->
                <div class="col-sm-12 search-collapse">
                    <form id="formId">
                        <input type="hidden" name="buildingId" id="buildingId"/>
                        <input type="hidden" name="unitId" id="unitId"/>
                        <div class="select-list">
                            <ul>
                                <li>
                                    <label>搜索：</label>
                                    <input type="text" name="searchKeyword" placeholder="请输入楼栋号或单元号" onkeypress="if(event.keyCode==13) $.table.search()"/>
                                </li>
                                <li>
                                    <label>房间号：</label>
                                    <input type="text" name="room" onkeypress="if(event.keyCode==13) $.table.search()"/>
                                </li>
                                <li>
                                    <label>楼栋号：</label>
                                    <input type="text" name="buildingName" placeholder="请选择左侧楼栋单元" readonly/>
                                </li>
                                <li style="display: none;">
                                    <label>单元号：</label>
                                    <input type="text" name="unitName" readonly/>
                                </li>
                                <li>
                                    <label>房屋状态：</label>
                                    <select name="house_status">
                                        <option value="">所有</option>
                                        <option value="2002">未销售</option>
                                        <option value="2001">已入住</option>
                                        <option value="2003">已交房</option>
                                        <option value="2005">已装修</option>
                                        <option value="2004">未入住</option>
                                        <option value="2009">装修中</option>
                                    </select>
                                </li>
                                <li>
                                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm ml-10" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>

                <!-- 工具栏和表格区域 -->
                <div class="btn-group-sm" id="toolbar" role="group">
                    <a class="btn btn-success" onclick="$.operate.add()">
                        <i class="fa fa-plus"></i> 新增房屋
                    </a>
                    <a class="btn btn-warning ml-10" onclick="importData()">
                        <i class="fa fa-file-excel-o"></i> 批量导入
                    </a>
                    <a class="btn btn-danger ml-10 multiple disabled" onclick="$.operate.removeAll()">
                        <i class="fa fa-remove"></i> 删除
                    </a>
                </div>
                <div class="col-sm-12 select-table table-striped">
                    <table id="bootstrap-table"></table>
                </div>
            </div>
        </div>
    </div>

    <!-- 右键菜单 -->
    <div id="rightClickMenu" class="dropdown-menu" style="position: absolute; display: none; z-index: 9999; min-width: 120px; padding: 5px 0; background: white; border: 1px solid #ccc; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.15);">
        <ul class="list-unstyled" style="margin: 0;">
            <li class="menu-building-edit"><a href="javascript:void(0)" onclick="editBuilding()" style="display: block; padding: 8px 15px; color: #333; text-decoration: none; font-size: 12px;"><i class="fa fa-edit" style="margin-right: 5px;"></i>编辑楼宇</a></li>
            <li class="menu-building-add"><a href="javascript:void(0)" onclick="addBuilding()" style="display: block; padding: 8px 15px; color: #333; text-decoration: none; font-size: 12px;"><i class="fa fa-plus" style="margin-right: 5px;"></i>新增楼宇</a></li>
            <li class="menu-unit-edit"><a href="javascript:void(0)" onclick="editUnit()" style="display: block; padding: 8px 15px; color: #333; text-decoration: none; font-size: 12px;"><i class="fa fa-edit" style="margin-right: 5px;"></i>编辑单元</a></li>
            <li class="menu-unit-add"><a href="javascript:void(0)" onclick="addUnit()" style="display: block; padding: 8px 15px; color: #333; text-decoration: none; font-size: 12px;"><i class="fa fa-plus" style="margin-right: 5px;"></i>新增单元</a></li>
            <li class="menu-house-add"><a href="javascript:void(0)" onclick="addHouse()" style="display: block; padding: 8px 15px; color: #333; text-decoration: none; font-size: 12px;"><i class="fa fa-plus" style="margin-right: 5px;"></i>新增房屋</a></li>
        </ul>
    </div>


    <th:block th:include="include :: footer" />
    <th:block th:include="include :: layout-latest-js" />
    <th:block th:include="include :: ztree-js" />
    <script th:inline="javascript">

        var prefix = ctx + "oc/house";
        var statusDatas = [
            { dictValue: "2002", dictLabel: "未销售" },
            { dictValue: "2001", dictLabel: "已入住" },
            { dictValue: "2003", dictLabel: "已交房" },
            { dictValue: "2005", dictLabel: "已装修" },
            { dictValue: "2004", dictLabel: "未入住" },
            { dictValue: "2009", dictLabel: "装修中" }
        ];

        var houseTypeDatas = [
            { dictValue: "1", dictLabel: "住宅" },
            { dictValue: "2", dictLabel: "商铺" }
        ];

        $(function() {
            var panehHidden = false;
            if ($(this).width() < 769) {
                panehHidden = true;
            }
            $('body').layout({ initClosed: panehHidden, west__size: 220, resizeWithWindow: false });

            var buildingId = [[${buildingId}]];
            var unitId = [[${unitId}]];

            if (buildingId) {
                $("#buildingId").val(buildingId);
            }
            if (unitId) {
                $("#unitId").val(unitId);
            }

            loadBuildingZTree();
            
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "房屋信息",
                limit:30,
                layer:{
                    area:['800px','550px'],
                    offset: '70px'
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'combina_name',
                    title: '房屋',
                    formatter: function(value, row, index) {
                        var combina_name = row.combina_name || '';
                        var room = row.room || '';
                        var houseText = combina_name + '/' + room;
                        return '<a href="javascript:void(0)" onclick="houseDetail(\'' + row.house_id + '\')" style="color: #007bff;">' + houseText + '</a>';
                    }
                },
                {
                    field: 'floor',
                    title: '楼层'
                },
                {
                    field: 'use_area',
                    title: '使用面积',
                    formatter: function(value, row, index) {
                        return row.use_area+ "㎡";
                    }
                },                
                {
                    field: 'total_area',
                    title: '建筑面积',
                    formatter: function(value, row, index) {
                        return row.total_area+ "㎡";
                    }
                },
                {
                    field: 'owner_str',
                    title: '住户信息'
                },
                {
                    field: 'house_id',
                    title: '绑定住户',
                    visible: false,
                    formatter: function(value, row, index) {
                        var ownerCount = row.owner_count || 0;
                        if(ownerCount > 0) {
                            return ownerCount + '人 | <a href="javascript:void(0)" onclick="showOwners(\'' + row.house_id + '\')">查看住户</a>';
                        } else {
                            return '0人 | <a href="javascript:void(0)" onclick="showOwners(\'' + row.house_id + '\')">新增住户</a>';
                        }
                    }
                },
                {
                    field: 'house_type',
                    title: '住宅性质',
                    visible: false,
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(houseTypeDatas, value);
                    }
                },
                {
                    field: 'house_status',
                    title: '房屋状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                },{
                    field: 'room_tag',
                    title: '房屋标签',
                    visible: false
                },
                {
                    field: 'create_time',
                    title: '创建时间',
                    visible: false
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.house_id + '\')"><i class="fa fa-edit"></i> 编辑</a> ');
                        actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.house_id + '\')"><i class="fa fa-remove"></i> 删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);

            // 如果有URL参数，立即触发搜索
            if ((buildingId && buildingId !== 'null' && buildingId !== '') ||
                (unitId && unitId !== 'null' && unitId !== '')) {
                setTimeout(function() {
                    $.table.search();
                }, 100);
            }
        });

        // 房屋详情
        function houseDetail(houseId) {
            var url = prefix + "/owners/" + houseId;
            $.modal.popupRight({title:"房屋详情", url:url,id:'houseDetail',width:'800px'});
        }

        function loadBuildingZTree() {
            $.ajax({
                url: ctx + "oc/building/tree",
                type: "GET",
                success: function(res) {
                    if (res.code == 0) {
                        var zNodes = convertToZTreeNodes(res.data);
                        var setting = {
                            data: {
                                simpleData: {
                                    enable: true,
                                    idKey: "id",
                                    pIdKey: "pId",
                                    rootPId: null
                                }
                            },
                            view: {
                                showIcon: true
                            },
                            callback: {
                                onClick: function(event, treeId, treeNode) {
                                    if(treeNode.type === 'building') {
                                        $("#buildingId").val(treeNode.id);
                                        $("#unitId").val("");
                                        $("input[name='buildingName']").val(treeNode.name);
                                        $("input[name='unitName']").val("");
                                    } else if(treeNode.type === 'unit') {
                                        $("#buildingId").val(treeNode.pId);
                                        $("#unitId").val(treeNode.id);
                                        $("input[name='buildingName']").val(treeNode.buildingName);
                                        $("input[name='unitName']").val(treeNode.name);
                                    }
                                    $.table.search();
                                },
                                onRightClick: function(event, treeId, treeNode) {
                                    if (!treeNode) return;

                                    // 选中右键点击的节点
                                    var treeObj = $.fn.zTree.getZTreeObj(treeId);
                                    treeObj.selectNode(treeNode);

                                    // 显示右键菜单
                                    showRightClickMenu(event, treeNode);
                                }
                            }
                        };
                        $.fn.zTree.init($("#buildingTree"), setting, zNodes);
                    } else {
                        $.modal.alertError("加载楼栋单元树失败：" + res.msg);
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.alertError("加载楼栋单元树失败：" + error);
                }
            });
        }

        // 转换接口数据为ztree扁平结构
        function convertToZTreeNodes(data) {
            var nodes = [];
            $.each(data, function(i, building) {
                var bNode = {
                    id: building.buildingId,
                    pId: null,
                    name: building.buildingName.endsWith('栋') ? building.buildingName : building.buildingName + '栋',
                    type: 'building',
                    open: false
                };
                nodes.push(bNode);
                if(building.children && building.children.length > 0) {
                    $.each(building.children, function(j, unit) {
                        nodes.push({
                            id: unit.unitId,
                            pId: building.buildingId,
                            name: unit.unitName.endsWith('单元') ? unit.unitName : unit.unitName + '单元',
                            type: 'unit',
                            buildingId: building.buildingId,
                            buildingName: building.buildingName.endsWith('栋') ? building.buildingName : building.buildingName + '栋'
                        });
                    });
                }
            });
            return nodes;
        }

        // 展开所有节点
        $('#btnExpand').click(function() {
            var treeObj = $.fn.zTree.getZTreeObj("buildingTree");
            treeObj.expandAll(true);
            $(this).hide();
            $('#btnCollapse').show();
        });

        // 折叠所有节点
        $('#btnCollapse').click(function() {
            var treeObj = $.fn.zTree.getZTreeObj("buildingTree");
            treeObj.expandAll(false);
            $(this).hide();
            $('#btnExpand').show();
        });

        // 刷新树
        $('#btnRefresh').click(function() {
            loadBuildingZTree();
        });

        // 自定义重置
        function resetPre() {
            $("#formId")[0].reset();
            $("#buildingId").val("");
            $("#unitId").val("");
            $.table.search();
        }

        // 查看房屋绑定的住户
        function showOwners(houseId) {
            var url = prefix + "/owners/" + houseId;
            $.modal.openTab("房屋住户", url);
        }

        // 搜索树节点
        function searchTree() {
            var keyword = $("#treeSearchInput").val();
            var treeObj = $.fn.zTree.getZTreeObj("buildingTree");
            if (!treeObj) return;

            if (keyword) {
                // 获取所有节点
                var allNodes = treeObj.getNodes();
                var nodesToShow = [];

                // 递归搜索所有节点
                function searchNodes(nodes) {
                    for (var i = 0; i < nodes.length; i++) {
                        var node = nodes[i];
                        if (node.name && node.name.indexOf(keyword) !== -1) {
                            // 匹配的节点
                            nodesToShow.push(node);

                            // 添加所有父节点
                            var parent = node.getParentNode();
                            while (parent) {
                                if (nodesToShow.indexOf(parent) === -1) {
                                    nodesToShow.push(parent);
                                }
                                parent = parent.getParentNode();
                            }

                            // 添加所有子节点
                            function addChildNodes(parentNode) {
                                if (parentNode.children && parentNode.children.length > 0) {
                                    for (var j = 0; j < parentNode.children.length; j++) {
                                        var childNode = parentNode.children[j];
                                        if (nodesToShow.indexOf(childNode) === -1) {
                                            nodesToShow.push(childNode);
                                        }
                                        addChildNodes(childNode);
                                    }
                                }
                            }
                            addChildNodes(node);
                        }
                        if (node.children && node.children.length > 0) {
                            searchNodes(node.children);
                        }
                    }
                }

                searchNodes(allNodes);

                if (nodesToShow.length > 0) {
                    // 隐藏所有节点
                    function hideAllNodes(nodes) {
                        for (var i = 0; i < nodes.length; i++) {
                            treeObj.hideNode(nodes[i]);
                            if (nodes[i].children && nodes[i].children.length > 0) {
                                hideAllNodes(nodes[i].children);
                            }
                        }
                    }
                    hideAllNodes(allNodes);

                    // 显示需要显示的节点
                    for (var i = 0; i < nodesToShow.length; i++) {
                        treeObj.showNode(nodesToShow[i]);
                    }

                    // 展开父节点并选中第一个匹配的节点
                    var firstMatchNode = null;
                    for (var i = 0; i < nodesToShow.length; i++) {
                        var node = nodesToShow[i];
                        if (node.name && node.name.indexOf(keyword) !== -1) {
                            if (!firstMatchNode) {
                                firstMatchNode = node;
                            }
                            // 展开匹配节点的父节点
                            var parent = node.getParentNode();
                            while (parent) {
                                treeObj.expandNode(parent, true, false, false);
                                parent = parent.getParentNode();
                            }
                        }
                    }

                    if (firstMatchNode) {
                        treeObj.selectNode(firstMatchNode, true);
                    }
                } else {
                    $.modal.alertWarning("未找到匹配的楼栋或单元");
                }
            } else {
                // 清空搜索，显示所有节点
                var allNodes = treeObj.getNodes();
                function showAllNodes(nodes) {
                    for (var i = 0; i < nodes.length; i++) {
                        treeObj.showNode(nodes[i]);
                        if (nodes[i].children && nodes[i].children.length > 0) {
                            showAllNodes(nodes[i].children);
                        }
                    }
                }
                showAllNodes(allNodes);
                treeObj.expandAll(false);
            }
        }

        // 清空树搜索
        function clearTreeSearch() {
            $("#treeSearchInput").val("");
            searchTree(); // 调用搜索函数来重置显示
        }

        // 管理楼栋
        function buildingMgr(){
            var url = ctx + "oc/building/mgr";
            $.modal.openTab("楼栋管理", url);
        }
        function importData(){

        }

        // 全局变量存储当前右键点击的节点
        var currentRightClickNode = null;

        // 显示右键菜单
        function showRightClickMenu(event, treeNode) {
            currentRightClickNode = treeNode;
            var menu = $("#rightClickMenu");
            var menuItems = menu.find("li");

            // 隐藏所有菜单项
            menuItems.hide();

            if (treeNode.type === 'unit') {
                // 单元右键菜单：编辑单元、新增单元、新增房屋
                menu.find(".menu-unit-edit").show();
                menu.find(".menu-unit-add").show();
                menu.find(".menu-house-add").show();
            } else if (treeNode.type === 'building') {
                // 楼栋右键菜单：编辑楼宇、新增楼宇、新增单元
                menu.find(".menu-building-edit").show();
                menu.find(".menu-building-add").show();
                menu.find(".menu-unit-add").show();
            }

            // 显示菜单
            menu.css({
                left: event.pageX + "px",
                top: event.pageY + "px",
                display: "block"
            });

            // 阻止默认右键菜单
            event.preventDefault();
            return false;
        }

        // 隐藏右键菜单
        function hideRightClickMenu() {
            $("#rightClickMenu").hide();
        }

        // 点击其他地方隐藏右键菜单
        $(document).click(function() {
            hideRightClickMenu();
        });

        // 编辑楼栋
        function editBuilding() {
            if (!currentRightClickNode) return;
            var buildingId = currentRightClickNode.type === 'building' ? currentRightClickNode.id : currentRightClickNode.buildingId;
            var url = ctx + "oc/building/edit/" + buildingId;
            $.modal.open("编辑楼栋", url);
            hideRightClickMenu();
        }

        // 新增楼栋
        function addBuilding() {
            var url = ctx + "oc/building/add";
            $.modal.open("新增楼栋", url);
            hideRightClickMenu();
        }

        // 编辑单元
        function editUnit() {
            if (!currentRightClickNode || currentRightClickNode.type !== 'unit') return;
            var url = ctx + "oc/unit/edit/" + currentRightClickNode.id;
            $.modal.open("编辑单元", url);
            hideRightClickMenu();
        }

        // 新增单元
        function addUnit() {
            if (!currentRightClickNode) return;
            var buildingId = currentRightClickNode.type === 'building' ? currentRightClickNode.id : (currentRightClickNode.buildingId || currentRightClickNode.pId);
            var url = ctx + "oc/unit/add?buildingId=" + buildingId;
            $.modal.open("新增单元", url);
            hideRightClickMenu();
        }

        // 新增房屋
        function addHouse() {
            if (!currentRightClickNode || currentRightClickNode.type !== 'unit') return;
            var buildingId = currentRightClickNode.buildingId || currentRightClickNode.pId;
            var unitId = currentRightClickNode.id;
            var url = ctx + "oc/house/add?buildingId=" + buildingId + "&unitId=" + unitId;
            $.modal.open("新增房屋", url);
            hideRightClickMenu();
        }
    </script>
</body>
</html> 